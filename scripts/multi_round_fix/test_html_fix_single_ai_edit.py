import json
import os

from app.enums.task import TaskType
from scripts import COOKIE
from worker.xdoc_html_fix_worker import XdocHtmlFixWorker
import requests
from app.basic.storagehandler import storage
from worker.title_level_worker import Title<PERSON>evelWorker
from app.basic.log import logger


class TestXdocHtmlFix:

    def __init__(self, task_info: dict):
        self.task_info = task_info
        self.tag = 'ai-edit'

    async def main(self):
        task_id = self.task_info['task_id']
        app_key = self.task_info['app_key']
        machine_html_url = self.task_info['html_url']
        subject = self.task_info['subject']
        word_type = self.task_info['word_type']

        # 测试 HTML 片段
        # machine_html_url = 'https://hexin-worksheet.oss-cn-shanghai.aliyuncs.com/static/ai-util/test_html_fix.html'

        # 前置跑标题
        # callback_info = TitleLevelWorker(
        #     task_id=tid,
        #     html_url=machine_html_url,
        #     subject=subject,
        #     bucket_name='xdoc-stable',
        #     upload_path='open/fc7539b21810cd4f0f0fb620/task/19123911.html',
        #     is_ai_edit=True,
        #     is_test=True,
        # ).main()
        # machine_html_url = 'https://hexin-worksheet.oss-cn-shanghai.aliyuncs.com/open/fc7539b21810cd4f0f0fb620/task/19123911.html'

        logger.info(f"task_id={task_id}")
        logger.info(f"subject={subject}")
        logger.info(f"app_key={app_key}")
        logger.info(f"machine_html_url={machine_html_url}")
        callback_info = await XdocHtmlFixWorker(
            task_id=task_id,
            html_url=machine_html_url,
            subject=subject,
            app_key=app_key,
            bucket_name='xdoc-stable',
            upload_path='open/fc7539b21810cd4f0f0fb620/task/20474802.html',
            word_type=word_type,
            is_ai_edit=True,
            task_type=TaskType.XDOC_HTML_FIX_PRIORITY.value,
            is_test=True,
            tag=self.tag,
        ).main()
        status = callback_info['status']  # 0 成功  1 失败


if __name__ == '__main__':
    import asyncio
    # tag: jxw  daily_fbd

    task_info = {
        "task_id": "685e7fafc256dbaf609b5ebf",
        "word_url": "https://sigma-temp.oss-cn-shanghai.aliyuncs.com/temp_docx/00e8191d.docx",
        "html_url": "https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/fc7539b21810cd4f0f0fb620/ai/685e7fafc256dbaf609b5ebf.html?time=1751023538093",
        "is_ai_edit": True,
        "subject": "english",
        "stage": "junior",
        "app_key": "fc7539b21810cd4f0f0fb620",
        "word_type": "wps-analysis",
        "bucket_name": "xdoc-stable",
    }
    asyncio.run(TestXdocHtmlFix(
        task_info=task_info
    ).main())
