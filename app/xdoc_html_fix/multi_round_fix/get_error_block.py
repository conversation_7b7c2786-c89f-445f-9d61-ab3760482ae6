# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/5/12 15:31
import copy
import json
import re

from app.basic.api.xdoc import XdocApi
from app.basic.util import utils
from app.enums.node import QuestionType, ERROR_INFO_WHITE_LIST
from app.basic.util import html_util


class GetErrorBlock:

    def __init__(self, html_list: list, round_index: int, success_sn_list: list):
        self.html_list = html_list
        self.round_index = round_index
        self.success_sn_list = success_sn_list
        self.html_data = html_util.join_html(self.html_list, is_del_line=False)

    def main(self):
        """
        获取需要 fix 的 block，使用 json check error 做筛选
        """
        node_list = self.get_node_list()
        # 获取失败的 block
        sub_html_list = []
        for index, node in enumerate(node_list):
            if 'A.水的体积' in str(node):
                print()

            node_type = node['node_type']
            if node_type == 'chapter':
                continue
            if not self.has_error(node):
                continue
            if node.get('is_merge'):
                continue
            block_list = self.get_block(node, node_list, index)
            sub_html_list += block_list

        # 收集所有的已经跑过的题目前缀
        line_list = []
        for item in sub_html_list:
            prefix_text = item['prefix_text']
            # 先取消自检通过就不跑下一轮，以 json check 结果为主
            # 已经成功的就不跑了
            # if prefix_text in self.success_sn_list:
            #     continue
            line_list.append(prefix_text)
        return sub_html_list, line_list

    def get_node_list(self):
        """
        使用 xdoc 接口 html 转 json
        把 json 打平
        """
        result = XdocApi.html_to_json(self.html_data)
        temp = json.dumps(result)
        # 打平树
        node_list = []
        for item in utils.iter_tree(result):
            node = copy.deepcopy(item.node)
            node_type = node['node_type']
            if node_type in ('chapter', 'paragraph'):
                node.pop('children', '')
            if node_type == 'question':
                item.stop_iter_subs()
            node_list.append(node)
        return node_list

    def has_error(self, node):
        """
        判断 node 内是否有错误
        node 为大小题情况下，如果子题有 error，则大小题一起重新处理
        """
        error_list = []

        def recursion(d):
            nonlocal error_list
            error_info = d.get('errorInfo', [])
            error_list += error_info
            if d.get('children'):
                for c in d['children']:
                    recursion(c)
        # 拿到所有的 error
        recursion(node)

        for item in error_list:
            rule, _type = item['rule'], item['type']
            if _type != 'error':
                continue
            return True
        return False

    def get_block(self, node, node_list, index):
        """
        根据 node 获取对应的 html 内容
        获取 node 中的最小的 line 和最大的 line，然后去 html_data 中切割
        """
        serial_number = node.get('content', {}).get('serial_number', '')
        node_type = node['node_type']

        # 如果当前节点是 paragraph，则先向上找，再向下找 block
        # 如果 question，向下找 block
        if node_type == 'paragraph' and index > 0:
            # 倒序往上找 5 个
            for i in range(index - 1, index - 6, -1):
                if index < 0:
                    break
                pre_node = node_list[i]
                pre_node_type, is_merge = pre_node['node_type'], pre_node.get('is_merge', False)
                if is_merge:
                    break
                body = node['content'].get('body', '')
                if pre_node_type == 'chapter':
                    break
                elif pre_node_type in ('paragraph', 'question'):
                    pre_node['is_merge'] = True
                    node['content']['body'] = str(pre_node) + body
                    node['question_type'] = pre_node.get('question_type', '')
                    if pre_node_type == 'question':
                        break

        # 1.如果下面节点是段落节点，则合并到当前node
        # 2.如果下面是试题节点且试题节点的内容所在的行在当前 node 的范围内，则把试题节点合并到当前 node
        # 3.如果是标题节点，跳出
        node_length = len(node_list)
        next_node_str = ''
        for i in range(index + 1, index + 16):
            node['is_merge'] = True
            # 拿到当前 node 的 line 的区间
            min_line, max_line = self.get_line_range(str(node))
            if i > node_length - 1:
                break
            analysis = node['content'].get('analysis', '')
            next_node = node_list[i]
            next_node_type = next_node['node_type']
            if next_node_type == 'chapter':
                # 下一个节点是标题，则直接跳出
                next_node_str = str(next_node)
                break
            elif next_node_type == 'paragraph':
                next_body = next_node.get('content', {}).get('body', '')
                # 如果是真的段落，则证明上个题结束了，跳出
                if not self._is_real_paragraph(next_body):
                    break
                # 标记一下，此 node 被合并了
                next_node['is_merge'] = True
                node['content']['analysis'] = analysis + str(next_node)
                continue
            elif next_node_type == 'question':
                # 下一个节点是试题
                next_min_line, next_max_line = self.get_line_range(str(next_node))
                if self._is_merge_next_question(node, next_node, next_min_line, min_line, max_line):
                    # 被合并到上一题说明这个应该是一个单题，清掉 origin html 中的 quest_num 及 level 标签
                    for sub_i in range(next_min_line, next_max_line + 1):
                        tmp = self.html_list[sub_i]
                        tmp = re.sub(r'<span[^<]*?data-label="quest_num"\s+data-level="\d+"[^<]*?>', '<span>', tmp)
                        self.html_list[sub_i] = tmp
                    # 标记一下，此 node 被合并了
                    next_node['is_merge'] = True
                    node['content']['analysis'] = analysis + str(next_node)
                    continue
                else:
                    break

        data = str(node)
        min_line, max_line = self.get_line_range(data, next_node_str)
        if min_line is None and max_line is None:
            return []
        sub_html_list = self.html_list[min_line: max_line + 1]
        question_type = node.get('question_type', '')
        res = []
        # 材料题
        if question_type == QuestionType.material.name:
            sub_html_str = html_util.join_html(sub_html_list, is_del_line=False)
            prefix_text = html_util.get_prefix_text(sub_html_str)
            # debug
            # if '下图为北京时间' in sub_html_str:
            res.append({
                'html_data': sub_html_str,
                'min_line': min_line,
                'max_line': max_line,
                'question_type': question_type,
                'serial_number': serial_number,
                'prefix_text': prefix_text,
            })
        # 其余题
        else:
            # 可能存在多题，拆分为单题一组
            sub_html_group = []
            tmp = []
            for sub_i, html_str in enumerate(sub_html_list):
                if '首先利用指对互化' in html_str:
                    print()

                if sub_i == 0 and 'data-level="1"' in html_str:
                    tmp.append(html_str)
                    if len(sub_html_list) == 1:
                        sub_html_group.append('\n'.join(tmp))
                elif 'data-level="1"' in html_str and 'data-label="explanation"' not in html_str and 'data-label="answer"' not in html_str:
                    sub_html_group.append('\n'.join(tmp))
                    tmp = [html_str]
                elif sub_i == len(sub_html_list) - 1:
                    tmp.append(html_str)
                    sub_html_group.append('\n'.join(tmp))
                else:
                    tmp.append(html_str)

            for sub_html_str in sub_html_group:
                # debug
                # if '小娅为了准确测量出一个圆锥铁块的底面积' not in sub_html_str:
                #     continue

                # 重新判断题型
                question_type = self.get_question_type(sub_html_str, node.get('question_type', ''))
                min_line, max_line = self.get_line_range(sub_html_str)
                prefix_text = html_util.get_prefix_text(sub_html_str)
                serial_number = html_util.get_serial_number(sub_html_str)
                res.append({
                    'html_data': sub_html_str,
                    'min_line': min_line,
                    'max_line': max_line,
                    'question_type': question_type,
                    'serial_number': serial_number,
                    'prefix_text': prefix_text,
                })
        return res

    def get_question_type(self, data, question_type):
        """ 判断题型 """
        if question_type == QuestionType.other.name:
            if self._is_other(data):
                return QuestionType.other.name
            elif self._is_choice(data):
                return QuestionType.choice.name

        if question_type != QuestionType.blank.name:
            return question_type
        if 'data-label="quest_num"' in data and 'data-level="2"' in data:
            if self._is_material(data):
                return QuestionType.material.name
            else:
                return QuestionType.other.name
        else:
            if self._is_other(data):
                return QuestionType.other.name
        return question_type

    def _is_real_paragraph(self, s):
        """
        判断是否是真正的 paragraph
        先简单判断下，需要补逻辑
        """
        invalid_keywords = [r'答题卡', r'题号', r'本题包括']
        for k in invalid_keywords:
            if re.search(k, s):
                return False
        return True

    def get_line_range(self, data, next_node_str=''):
        """
        获取行号的范围
        # 获取最小行号和最大行号
        # 最小行号：data_line_list 的最小值
        # 最大行号：next_data_line_list 的最小值减一
        """
        data_line_list = re.findall(r' line="(\d+)"', data)
        next_data_line_list = re.findall(r' line="(\d+)"', next_node_str)
        if not data_line_list:
            return None, None

        def _helper(lst):
            # 如果当前行号比上一个行号大超过 5，则认为当前行号不合理，使用上一个行号作为最大行号
            lst = [int(l) for l in lst]
            lst.sort()
            _min = pred_s = lst[0]
            _max = lst[-1]
            for s in lst:
                if s - pred_s > 5:
                    _max = pred_s
                    break
                pred_s = s
            return _min, _max

        # 获取最小行号和最大行号
        # 最小行号：data_line_list 的最小值
        # 最大行号：next_data_line_list 的最小值减一
        min_line, max_line = _helper(data_line_list)
        if next_data_line_list:
            next_min_line, next_max_line = _helper(next_data_line_list)
            max_line = next_min_line - 1

        # 试题结尾重新圈选
        while True:
            if not self.html_list[max_line].startswith('<p'):
                max_line -= 1
            else:
                break
        return min_line, max_line

    def _is_material(self, data):
        if '材料' in data:
            return True
        return False

    def _is_other(self, data):
        if re.search(r'<span[^<>]*?data-level="1"[^<>]*?>', data) and '（1）' in data and '（2）' in data:
            return True
        return False

    def _is_choice(self, data):
        data_lst = data.split('</p>')
        body = ''
        for d in data_lst:
            if 'data-label="answer"' in d:
                break
            body += d

        if 'A.' in body and 'B.' in body and 'C.' in body and 'D.' in body:
            return QuestionType.choice.name

    def _is_merge_next_question(
            self, node: dict, next_node: dict, next_min_line: int, min_line: int, max_line: int):
        """
        判断是否把下一个 node merge 到当前 node
        """
        # 最小行在上一节点的行区间内，则把此节点附加到上一节点
        if min_line < next_min_line < max_line:
            return True

        # 1.当前题有答案或解析，下一题只有题干无答案和解析 2.当前题号大于下一题题号
        content = node['content']
        next_content = next_node['content']
        serial_number = content.get('serial_number', '')
        next_serial_number = next_content.get('serial_number', '')
        answer = content.get('answer')
        next_answer = next_content.get('answer')
        analysis = content.get('analysis')
        next_analysis = next_content.get('analysis')
        if (answer or analysis) and not next_answer and not next_analysis and (serial_number > next_serial_number):
            return True
        return False


