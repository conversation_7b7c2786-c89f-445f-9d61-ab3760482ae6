# -*-coding:utf-8 -*-
# Author     ：AI Assistant
# Time       ：2025/6/26
import re
import json
import asyncio
# from typing import List, Dict, Any, Tuple  # 注释掉类型注解以兼容旧版本Python

from app.basic.util import html_util
from app.enums.prompt import GetPrompt
from app.basic.api.doubao import <PERSON><PERSON><PERSON>, DBModel
from app.basic.log import logger


class PreAnswer:
    """
    AI模型答案分析处理模块
    负责对简化后的HTML数据进行AI答案判断和处理
    """

    def __init__(self, subject, task_id):
        self.subject = subject
        self.task_id = task_id

    @staticmethod
    def transform_html_data_for_answer(html_data):
        """
        将HTML数据转换为PreAnswer处理所需的格式

        Args:
            html_data: 原始HTML数据字符串

        Returns:
            tuple: (simplified_html_list, html_elements)
                - simplified_html_list: 简化的HTML列表，格式为 "index:data-label@content"
                - html_elements: 原始HTML元素列表
        """
        # 重新解析HTML数据，准备答案处理
        html_elements = html_util.split_html_v2(html_data)
        simplified_html_list = []

        for index, element in enumerate(html_elements):
            # 只提取 p 标签上的 data-label 属性
            data_label = ""
            if element.strip().startswith('<p'):
                # 提取 p 标签上的 data-label 属性
                p_tag_match = re.search(r'<p[^>]*data-label="([^"]*)"[^>]*>', element)
                data_label = p_tag_match.group(1) if p_tag_match else ""

            # 先处理 table 和 img 标签，将它们替换为占位符
            # 处理完整的 table 标签（包括嵌套内容）
            processed_element = re.sub(r'<table[^>]*>.*?</table>', '@table@', element, flags=re.DOTALL)
            # 处理 img 标签（自闭合标签）
            processed_element = re.sub(r'<img[^>]*/?>', '@img@', processed_element)

            # 去掉所有 HTML 标签，保留内容
            content = html_util.del_html_tag(processed_element).strip()

            # 组合成指定格式：index:data-label@content
            simplified_item = "{}:{}@{}".format(index, data_label, content)
            simplified_html_list.append(simplified_item)

        return simplified_html_list, html_elements

    async def process_html_data(self, html_data):
        """
        一站式处理HTML数据：转换 + AI答案判断

        Args:
            html_data: 原始HTML数据字符串

        Returns:
            tuple: (更新后的HTML数据, cost_token)
        """
        # 转换HTML数据
        simplified_html_list, html_elements = self.transform_html_data_for_answer(html_data)

        # 执行AI答案判断
        return await self.analyze_simplified_html_list(simplified_html_list, html_elements)

    async def analyze_simplified_html_list(self, simplified_html_list, html_elements):
        """
        分析简化后的HTML列表，执行AI答案判断并应用结果

        Args:
            simplified_html_list: 简化的HTML列表，格式为 "index:data-label@content"
            html_elements: 原始HTML元素列表

        Returns:
            tuple: (更新后的HTML数据, cost_token)
        """
        cost_token = 0

        # 处理简化后的数据列表
        processed_result = self.process_simplified_list(simplified_html_list)

        # 执行AI判断answer功能
        if processed_result:
            try:
                ai_judgment_result, ai_cost_token = await self.ai_judge_answer(processed_result)
                cost_token += ai_cost_token
                logger.info("AI答案判断完成，识别出需要重新标记的内容，消耗token: {}".format(ai_cost_token))

                # 应用AI判断结果到HTML数据
                self._apply_ai_judgment_to_html(ai_judgment_result, simplified_html_list, html_elements)

            except Exception as e:
                logger.error("AI答案判断失败: {}".format(e))
                # AI判断失败不影响主流程，继续执行

        # 重新构建HTML数据
        return html_util.join_html(html_elements), cost_token

    def process_simplified_list(self, simplified_html_list):
        """
        处理简化后的HTML列表，按照新的分块逻辑进行分组

        新的答案判断分块逻辑：
        1. 只根据 header 来分块
        2. 去掉解析逻辑（因为已经由ai判断过，认为一定对了）
        3. 每个header内的所有行一次性发送，不再分子块

        Args:
            simplified_html_list: 格式为 "index:data-label@content" 的列表

        Returns:
            list: 处理后的结果，格式为 [{index: 0, list: [all_content]}, ...]
        """
        # 1. 先按header分大块
        header_groups = self._split_by_header(simplified_html_list)

        # 2. 处理每个header组
        result = []
        result_index = 0

        for header_group in header_groups:
            if header_group:
                # 过滤掉explanation和header标签的行，只保留需要判断的内容
                filtered_content = self._filter_content_for_judgment(header_group)

                # 确保有足够的内容进行AI判断（至少2个项目）
                if filtered_content and len(filtered_content) >= 2:
                    # 每个header内的所有行作为一个整体，不再分子块
                    result.append({
                        'index': result_index,
                        'list': [filtered_content]  # 所有内容作为一个list
                    })
                    result_index += 1

        return result

    def _split_by_header(self, simplified_html_list):
        """
        按header标签分割列表
        
        Args:
            simplified_html_list: 完整的简化HTML列表
            
        Returns:
            list: 按header分割的组列表
        """
        groups = []
        current_group = []
        
        for item in simplified_html_list:
            # 解析当前项
            parts = item.split(':', 1)
            if len(parts) < 2:
                current_group.append(item)
                continue
                
            label_content = parts[1]
            label_parts = label_content.split('@', 1)
            if len(label_parts) < 2:
                current_group.append(item)
                continue
                
            data_label = label_parts[0]
            
            # 如果遇到header，开始新的组
            if data_label == 'header':
                if current_group:
                    groups.append(current_group)
                current_group = [item]
            else:
                current_group.append(item)
        
        # 添加最后一个组
        if current_group:
            groups.append(current_group)
            
        return groups



    def _filter_content_for_judgment(self, content_list):
        """
        过滤内容，移除explanation和header标签的行，只保留需要判断的内容
        
        Args:
            content_list: 内容列表
            
        Returns:
            list: 过滤后的内容列表
        """
        filtered_content = []
        
        for item in content_list:
            # 解析当前项
            parts = item.split(':', 1)
            if len(parts) < 2:
                filtered_content.append(item)
                continue
                
            label_content = parts[1]
            label_parts = label_content.split('@', 1)
            if len(label_parts) < 2:
                filtered_content.append(item)
                continue
                
            data_label = label_parts[0]
            
            # 忽略explanation和header标签的行
            if data_label not in ['explanation', 'header']:
                filtered_content.append(item)
        
        return filtered_content

    async def ai_judge_answer(self, processed_result, model=DBModel.DS_V3.value):
        """
        使用AI判断哪些内容应该被标记为answer
        现在每个header内的所有行一次性发送，简化处理逻辑

        Args:
            processed_result: process_simplified_list的处理结果
            model: 使用的AI模型

        Returns:
            tuple: (包含原始数据和AI判断结果的字典, cost_token)
        """
        logger.info("开始AI判断answer，共{}个header组".format(len(processed_result)))

        if not processed_result:
            return {
                'original_processed_result': processed_result,
                'ai_judgment_results': []
            }, 0

        # 并行处理所有header组，每个组内的所有数据一次性发送
        ai_results = []
        total_cost_token = 0

        # 创建并行处理任务
        tasks = []
        for group in processed_result:
            task = self._process_single_header_group(
                group['index'],
                group['list'][0],  # 现在每个组只有一个list，包含所有内容
                model
            )
            tasks.append(task)

        # 并行执行所有任务
        logger.info("开始并行处理{}个header组".format(len(tasks)))
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        for result in results:
            if isinstance(result, Exception):
                logger.error("处理header组时发生错误: {}".format(result))
                continue

            group_result, group_cost_token = result
            total_cost_token += group_cost_token

            if group_result:
                ai_results.append(group_result)

        # 按group_index排序确保结果顺序正确
        ai_results.sort(key=lambda x: x['group_index'])

        return {
            'original_processed_result': processed_result,
            'ai_judgment_results': ai_results
        }, total_cost_token

    async def _process_single_header_group(self, group_index, content_list, model):
        """
        处理单个header组的所有内容（一次性发送）

        Args:
            group_index: 组索引
            content_list: header组的所有内容列表
            model: AI模型

        Returns:
            tuple: (包含组索引的AI判断结果, cost_token)
        """
        logger.info("处理header组 {}，共{}行内容".format(group_index, len(content_list)))

        try:
            # 准备AI判断的输入数据（不需要上下文，所有数据一次性发送）
            input_data = self._prepare_ai_input_for_answer_simple(content_list)

            # 调用AI进行判断
            ai_result, ai_cost_token = await self._call_ai_judgment_for_answer(input_data, model)

            if ai_result:
                return {
                    'group_index': group_index,
                    'results': [{
                        'input_data': input_data,
                        'ai_result': ai_result,
                        'original_block': content_list
                    }]
                }, ai_cost_token
            else:
                return None, ai_cost_token

        except Exception as e:
            logger.error("处理header组 {} 失败: {}".format(group_index, e))
            return None, 0

    def _prepare_ai_input_for_answer_simple(self, content_list):
        """
        准备AI判断答案的输入数据，去掉所有label前缀，只保留行号和内容
        简化版本：不处理上下文，所有数据一次性发送

        Args:
            content_list: 内容列表

        Returns:
            str: 格式化的输入数据
        """
        input_lines = []

        # 处理所有内容
        for item in content_list:
            parts = item.split(':', 1)
            if len(parts) >= 2:
                index = parts[0]
                label_content = parts[1]
                label_parts = label_content.split('@', 1)
                if len(label_parts) >= 2:
                    content = label_parts[1]
                    # 只保留行号和内容，去掉所有label前缀
                    input_lines.append("{}:{}".format(index, content))

        result = '\n'.join(input_lines)
        logger.info("准备AI输入数据，共{}行内容".format(len(content_list)))
        return result



    async def _call_ai_judgment_for_answer(self, input_data, model):
        """
        调用AI进行答案判断

        Args:
            input_data: 输入数据
            model: AI模型

        Returns:
            tuple: (AI判断结果的ID列表, cost_token)
        """
        try:
            # 获取prompt
            prompt = GetPrompt.answer_fix(input_data)

            # 调用AI
            response, tokens = await Doubao.async_chat(prompt, model, temperature=0.1)

            logger.info("AI调用成功，使用tokens: {}".format(tokens))

            # 解析JSON响应
            try:
                # 清理响应中的markdown标记
                cleaned_response = self._clean_ai_response(response)
                result = json.loads(cleaned_response)
                if isinstance(result, list):
                    return result, tokens
                else:
                    logger.warning("AI返回格式不正确: {}".format(response))
                    return [], tokens
            except json.JSONDecodeError as e:
                logger.error("AI返回JSON解析失败: {}, response: {}".format(e, response))
                return [], tokens

        except Exception as e:
            logger.error("AI调用失败: {}".format(e))
            raise

    def _clean_ai_response(self, response):
        """
        清理AI响应中的markdown标记和其他格式化字符

        Args:
            response: 原始AI响应

        Returns:
            str: 清理后的JSON字符串
        """
        # 去除首尾空白
        cleaned = response.strip()

        # 移除可能的markdown代码块标记
        if cleaned.startswith('```json'):
            cleaned = cleaned[7:]  # 移除 '```json'
        elif cleaned.startswith('```'):
            cleaned = cleaned[3:]   # 移除 '```'

        if cleaned.endswith('```'):
            cleaned = cleaned[:-3]  # 移除结尾的 '```'

        # 再次去除空白
        cleaned = cleaned.strip()

        # 移除可能的其他格式化标记
        lines = cleaned.split('\n')
        json_lines = []
        in_json = False

        for line in lines:
            line = line.strip()
            # 跳过空行和注释行
            if not line or line.startswith('#') or line.startswith('//'):
                continue

            # 检查是否是JSON开始
            if line.startswith('[') or line.startswith('{'):
                in_json = True

            if in_json:
                json_lines.append(line)

            # 检查是否是JSON结束
            if line.endswith(']') or line.endswith('}'):
                break

        # 如果找到了JSON内容，返回拼接的结果
        if json_lines:
            result = '\n'.join(json_lines)
            logger.debug("清理后的AI响应: {}".format(result))
            return result

        # 如果没有找到明确的JSON结构，返回原始清理结果
        logger.debug("未找到明确JSON结构，返回清理后的响应: {}".format(cleaned))
        return cleaned

    def _apply_ai_judgment_to_html(self, ai_judgment_result, simplified_html_list, html_elements):
        """
        将AI判断结果应用到HTML数据中
        根据AI返回的行号列表，对HTML数据进行处理（添加或移除answer标签）

        Args:
            ai_judgment_result: AI判断结果
            simplified_html_list: 简化的HTML列表
            html_elements: 原始HTML元素列表
        """
        try:
            # 1. 提取所有AI识别为answer的行号
            ai_answer_line_numbers = self._extract_line_numbers_from_ai_result(ai_judgment_result)

            if not ai_answer_line_numbers:
                logger.info("AI判断结果中没有识别出answer内容")
                return

            logger.info("AI识别出 {} 个行号为answer: {}".format(len(ai_answer_line_numbers), ai_answer_line_numbers))

            # 2. 获取当前已标记为answer的行号
            current_answer_line_numbers = self._get_current_answer_line_numbers(simplified_html_list)
            logger.info("当前已标记为answer的行号: {}".format(current_answer_line_numbers))

            # 3. 计算需要添加和移除answer标签的行号
            lines_to_add = set(ai_answer_line_numbers) - set(current_answer_line_numbers)
            lines_to_remove = set(current_answer_line_numbers) - set(ai_answer_line_numbers)

            logger.info("需要添加answer标签的行号: {}".format(lines_to_add))
            logger.info("需要移除answer标签的行号: {}".format(lines_to_remove))

            # 4. 更新HTML元素
            updated_count = 0

            # 添加answer标签
            for line_number in lines_to_add:
                if self._update_html_element_answer(html_elements, line_number, add=True):
                    updated_count += 1

            # 移除answer标签
            for line_number in lines_to_remove:
                if self._update_html_element_answer(html_elements, line_number, add=False):
                    updated_count += 1

            logger.info("成功更新了 {} 个HTML元素的data-label属性".format(updated_count))

            # 5. 移除AI识别行号中的quest_num和choice_option标签
            removed_count = 0
            for line_number in ai_answer_line_numbers:
                if self._remove_quest_num_and_choice_option_labels(html_elements, line_number):
                    removed_count += 1

            logger.info("成功移除了 {} 个HTML元素的quest_num和choice_option标签".format(removed_count))

        except Exception as e:
            logger.error("应用AI判断结果失败: {}".format(e))
            # 不抛出异常，避免影响主流程

    def _extract_line_numbers_from_ai_result(self, ai_judgment_result):
        """
        从AI判断结果中提取所有被识别为answer的行号

        Args:
            ai_judgment_result: AI判断结果

        Returns:
            list: 被识别为answer的行号列表
        """
        line_numbers = []

        ai_results = ai_judgment_result.get('ai_judgment_results', [])
        for group_result in ai_results:
            # 简化结构：每个group现在只有一个result
            for sub_result in group_result.get('results', []):
                ai_result = sub_result.get('ai_result', [])
                if isinstance(ai_result, list):
                    line_numbers.extend(ai_result)

        # 去重并保持顺序
        unique_line_numbers = []
        seen = set()
        for line_number in line_numbers:
            if line_number not in seen:
                unique_line_numbers.append(line_number)
                seen.add(line_number)

        return unique_line_numbers

    def _get_current_answer_line_numbers(self, simplified_html_list):
        """
        获取当前已标记为answer的行号

        Args:
            simplified_html_list: 简化的HTML列表

        Returns:
            list: 当前已标记为answer的行号列表
        """
        answer_line_numbers = []

        for item in simplified_html_list:
            parts = item.split(':', 1)
            if len(parts) >= 2:
                line_number = parts[0]
                label_content = parts[1]
                label_parts = label_content.split('@', 1)
                if len(label_parts) >= 2:
                    data_label = label_parts[0]
                    if data_label == 'answer':
                        answer_line_numbers.append(line_number)

        return answer_line_numbers

    def _update_html_element_answer(self, html_elements, line_number, add):
        """
        更新指定行号的HTML元素的answer标签

        Args:
            html_elements: HTML元素列表
            line_number: 行号
            add: True表示添加answer标签，False表示移除

        Returns:
            bool: 是否成功更新
        """
        try:
            element_index = int(line_number)
            if 0 <= element_index < len(html_elements):
                original_element = html_elements[element_index]

                if add:
                    updated_element = self._add_or_update_answer_label(original_element)
                else:
                    updated_element = self._remove_answer_label(original_element)

                if updated_element != original_element:
                    html_elements[element_index] = updated_element
                    logger.debug("{}元素 {} 的answer标签".format('添加' if add else '移除', element_index))
                    return True
            else:
                logger.warning("元素索引 {} 超出范围".format(element_index))
        except ValueError:
            logger.warning("无法解析行号: {}".format(line_number))

        return False

    def _remove_answer_label(self, html_element):
        """
        移除HTML元素的data-label="answer"属性

        Args:
            html_element: 原始HTML元素字符串

        Returns:
            str: 移除answer标签后的HTML元素字符串
        """
        # 检查是否是p标签
        if not html_element.strip().startswith('<p'):
            return html_element

        # 使用正则表达式处理p标签
        p_tag_pattern = re.compile(r'<p([^>]*)>')
        match = p_tag_pattern.search(html_element)

        if not match:
            return html_element

        attributes = match.group(1)

        # 移除data-label="answer"属性
        data_label_pattern = re.compile(r'\s*data-label="answer"')
        new_attributes = data_label_pattern.sub('', attributes)

        # 清理多余的空格
        new_attributes = re.sub(r'\s+', ' ', new_attributes).strip()

        # 构建新的p标签
        if new_attributes:
            new_p_tag = f'<p {new_attributes}>'
        else:
            new_p_tag = '<p>'

        # 替换原始HTML中的p标签
        updated_element = p_tag_pattern.sub(new_p_tag, html_element)

        return updated_element

    def _add_or_update_answer_label(self, html_element):
        """
        为HTML元素添加或更新data-label="answer"属性

        Args:
            html_element: 原始HTML元素字符串

        Returns:
            str: 更新后的HTML元素字符串
        """
        # 检查是否是p标签
        if not html_element.strip().startswith('<p'):
            return html_element

        # 使用正则表达式处理p标签
        p_tag_pattern = re.compile(r'<p([^>]*)>')
        match = p_tag_pattern.search(html_element)

        if not match:
            return html_element

        attributes = match.group(1)

        # 检查是否已有data-label属性
        data_label_pattern = re.compile(r'\s*data-label="[^"]*"')

        if data_label_pattern.search(attributes):
            # 替换现有的data-label属性
            new_attributes = data_label_pattern.sub(' data-label="answer"', attributes)
        else:
            # 添加新的data-label属性
            new_attributes = attributes + ' data-label="answer"'

        # 清理多余的空格
        new_attributes = re.sub(r'\s+', ' ', new_attributes).strip()

        # 构建新的p标签
        if new_attributes:
            new_p_tag = f'<p {new_attributes}>'
        else:
            new_p_tag = '<p data-label="answer">'

        # 替换原始HTML中的p标签
        updated_element = p_tag_pattern.sub(new_p_tag, html_element)

        return updated_element

    def _remove_quest_num_and_choice_option_labels(self, html_elements, line_number):
        """
        移除指定行号的HTML元素中的quest_num和choice_option标签

        Args:
            html_elements: HTML元素列表
            line_number: 行号

        Returns:
            bool: 是否成功移除
        """
        try:
            element_index = int(line_number)
            if 0 <= element_index < len(html_elements):
                original_element = html_elements[element_index]
                updated_element = self._remove_specific_data_labels(original_element, ['quest_num', 'choice_option'])

                if updated_element != original_element:
                    html_elements[element_index] = updated_element
                    logger.debug("移除元素 {} 的quest_num和choice_option标签".format(element_index))
                    return True
            else:
                logger.warning("元素索引 {} 超出范围".format(element_index))
        except ValueError:
            logger.warning("无法解析行号: {}".format(line_number))

        return False

    def _remove_specific_data_labels(self, html_element, labels_to_remove):
        """
        移除HTML元素中指定的data-label属性

        Args:
            html_element: 原始HTML元素字符串
            labels_to_remove: 要移除的data-label值列表，如['quest_num', 'choice_option']

        Returns:
            str: 移除指定标签后的HTML元素字符串
        """
        updated_element = html_element

        for label in labels_to_remove:
            # 移除span标签中的指定data-label
            if label == 'quest_num':
                # 移除quest_num标签，包括可能的data-level属性
                pattern = re.compile(r'<span[^>]*?data-label="quest_num"[^>]*?>(.*?)</span>')
                updated_element = pattern.sub(r'\1', updated_element)
            elif label == 'choice_option':
                # 移除choice_option标签
                pattern = re.compile(r'<span[^>]*?data-label="choice_option"[^>]*?>(.*?)</span>')
                updated_element = pattern.sub(r'\1', updated_element)

            # 同时移除p标签上的对应data-label属性
            if updated_element.strip().startswith('<p'):
                p_tag_pattern = re.compile(r'<p([^>]*)>')
                match = p_tag_pattern.search(updated_element)

                if match:
                    attributes = match.group(1)
                    # 移除指定的data-label属性
                    data_label_pattern = re.compile(rf'\s*data-label="{label}"')
                    new_attributes = data_label_pattern.sub('', attributes)

                    # 如果是quest_num，还要移除data-level属性
                    if label == 'quest_num':
                        data_level_pattern = re.compile(r'\s*data-level="[^"]*"')
                        new_attributes = data_level_pattern.sub('', new_attributes)

                    # 清理多余的空格
                    new_attributes = re.sub(r'\s+', ' ', new_attributes).strip()

                    # 构建新的p标签
                    if new_attributes:
                        new_p_tag = f'<p {new_attributes}>'
                    else:
                        new_p_tag = '<p>'

                    # 替换原始HTML中的p标签
                    updated_element = p_tag_pattern.sub(new_p_tag, updated_element)

        return updated_element
