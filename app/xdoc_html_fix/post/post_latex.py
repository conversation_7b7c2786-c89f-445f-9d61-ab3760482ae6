# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/4/19 11:11
import re

from app.basic.util import latex_util


class PostLatex:

    def __init__(self, html_data: str, task_id: str):
        self.html_data = html_data
        self.task_id = task_id

    def main(self):
        self.post_by_latex()
        return self.html_data

    def post_by_latex(self):
        """
        后处理 latex
        """
        def repl(m):
            latex = m.group()
            # 中文括号统一换为英文括号
            latex = latex.replace('（', '(').replace('）', ')')
            # 符号统一加 //mathrm
            latex = self.symbols_wrap_mathrm(latex)
            # 符号统一去 //mathrm
            latex = self.symbols_del_mathrm(latex)
            latex = self.escape_latex(latex)
            return latex

        self.html_data = re.sub(r'\$\$.*?\$\$', repl, self.html_data)
        return self.html_data

    def symbols_wrap_mathrm(self, latex: str):
        """
        下面枚举的符号，如果不是正体，修改为正体，加 mathrm
        """
        symbols_1 = [
            'SAS', 'SSS', 'ASA', 'AAS', 'AAA', 'HL', '\\mu', '\\pi', '\\Omega', 'lim', 'cos',
            'sin', 'tan', 'cot', 'sec', 'csc'
        ]
        symbols_2 = ['μ', 'π', 'Ω']
        patterns = []
        for s in sorted(symbols_1):
            patterns.append((
                re.compile(r'(?<!\\)(\\mathrm)?{?(?<!\\mathrm{' + re.escape(s) + r'})' + re.escape(s) + r'(?!})'),
                r'\\mathrm{' + re.escape(s) + r'}'
            ))
        for s in sorted(symbols_2):
            patterns.append((
                re.compile(r'(?<!\\mathrm{' + re.escape(s) + r'})' + re.escape(s)),
                r'\\mathrm{' + re.escape(s) + r'}'
            ))

        # 按顺序应用所有替换规则
        for pattern, replacement in patterns:
            latex = pattern.sub(replacement, latex)
        return latex

    def symbols_wrap_mathrm(self, text):
        """
        下面枚举的符号，如果不是正体，修改为正体，加 \\mathrm
        """
        symbols = [
            'SAS', 'SSS', 'ASA', 'AAS', 'AAA', 'HL', '\\mu', '\\pi', '\\Omega', 'lim', 'cos',
            'sin', 'tan', 'cot', 'sec', 'csc', 'μ', 'π', 'Ω'
        ]
        # 移除所有现有的\mathrm{}
        for symbol in symbols:
            # 处理带反斜杠的符号
            pattern = re.compile(r'\\mathrm\{((' + re.escape(symbol) + r'))\}')
            text = pattern.sub(r'\1', text)

        # 为所有未包裹的符号添加\mathrm{}
        for symbol in symbols:
            # 处理普通符号（不在\mathrm{}中的）
            # r'(?<!\\)(\\mathrm\{[^}]*\})*(?<!\\)(' + re.escape(symbol) + r')(?!\})'
            pattern = re.compile(r'(?<!\\)(\\mathrm\{[^}]*\})*(?<!\\)(' + re.escape(symbol) + r')')
            text = pattern.sub(r'\1\\mathrm{\2}', text)
        return text

    def symbols_del_mathrm(self, text):
        """
        下面枚举的符号，如果是正体，则去掉 mathrm
        """
        symbols = [
            '\\alpha', 'α', '\\beta', 'β', '\\gamma', 'γ', '\\delta', 'δ', '\\epsilon', 'ϵ', '\\zeta', 'ζ',
            '\\eta', 'η', '\\theta', 'θ', '\\iota', 'ι', '\\kappa', 'κ', '\\lambda', 'λ', '\\nu', 'ν', '\\xi', 'ξ',
            '\\rho', 'ρ', '\\sigma', 'σ', '\\tau', 'τ', '\\upsilon', 'υ', '\\phi', 'φ', '\\varphi', 'φ',
            '\\chi', 'χ', '\\psi', 'ψ', '\\omega', 'ω',
        ]
        # 创建正则表达式模式，匹配 \\mathrm{symbol} 中的 symbol
        pattern = r'\\mathrm\{(' + '|'.join(map(re.escape, symbols)) + r')\}'
        # 替换为匹配到的symbol本身
        result = re.sub(pattern, r'\1', text)
        return result

    def escape_latex(self, latex: str):
        latex = latex.replace('&', '&amp;').replace('<', '\\lt ').replace('>', '\\gt ')
        return latex


if __name__ == '__main__':
    res = PostLatex(
        html_data=r'$$\alpha \in \left(\frac{\pi}{2},\mathrm{\pi} \right)$$',
        task_id='1111'
    ).main()
    print(res)
