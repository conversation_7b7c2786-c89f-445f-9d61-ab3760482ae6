# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/3/5 15:44
import re

from bs4 import BeautifulSoup

from app.basic.util import html_util
from app.basic.log import logger


class PreHtmlGlobal:

    def __init__(self, html_data: str, subject: str, task_id: str):
        self.html_data = html_data
        self.subject = subject
        self.task_id = task_id

    def main(self):
        logger.info(f"{self.task_id} PreHtmlGlobal start.")
        self.blank_fix()
        self.remove_choice_option_in_table()

        # 前序流程出来的 html，span 可能与属性之间无空格，例如：</spanstyle="font-family:楷体;;vertical-align:middle"> <spanstyle="font-family:楷体;;vertical-align:middle">
        # 对 span 和后面属性之间缺少空格的情况做处理
        self.html_data = re.sub(r'(<span)([^\s]*?[^>]*?>)', lambda x: x.group(1) + ' ' + x.group(2), self.html_data)
        self.html_data = re.sub(r'</span[^>]*?>', '</span>', self.html_data)

        # 前序流程出来的关键词、特殊标签的替换
        self.html_data = self.html_data.replace('HXDOLLAR', '$')
        self.html_data = re.sub(r'</?semic_table_title>', '', self.html_data)

        logger.info(f"{self.task_id} PreHtmlGlobal success.")
        return self.html_data

    def blank_fix(self):
        """ 填空问题处理 """
        def repl(m):
            text = m.group(1)
            search_res = re.search(r'data-length="(\d+)"', text)
            if search_res:
                length = search_res.group(1)
            else:
                length = 5
            result = text + '&nbsp;' * int(length) + '</span>。'
            result = html_util.add_check_span(result)
            return result
        # 丢失 </span> 和 &nbsp;
        self.html_data = re.sub(r'(<span[^<>]*?data-label="blank"[^<>]*?>)。', repl, self.html_data)

    def remove_choice_option_in_table(self):
        """ 清除掉表格内的选项字母标记 """
        soup = BeautifulSoup(self.html_data, 'html.parser')
        # 查找所有 table 标签
        tables = soup.find_all('table')
        for table in tables:
            # 查找 table 内所有符合条件的 span 标签
            spans = table.find_all('span', {'data-label': 'choice_option'})
            for span in spans:
                # 保留 span 标签内的内容
                span.unwrap()
        self.html_data = str(soup)
